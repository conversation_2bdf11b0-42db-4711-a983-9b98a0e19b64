/**
 * Utility for dynamically loading images from directories
 */

import { remove<PERSON>hit<PERSON><PERSON>ackground, has<PERSON><PERSON><PERSON><PERSON>ackground } from './backgroundRemover';

/**
 * Load all images from a directory
 * @param {string} directory - Directory path relative to public folder
 * @param {string[]} extensions - Allowed file extensions
 * @returns {Promise<string[]>} - Array of image URLs
 */
const loadImagesFromDirectory = async (directory, extensions = ['png', 'jpg', 'jpeg', 'webp']) => {
  try {
    // Since we can't directly read directory contents in the browser,
    // we'll use a predefined list based on what we know exists
    const imageFiles = await getImageFilesFromDirectory(directory);

    // Filter by extensions and return full paths
    return imageFiles
      .filter(file => extensions.some(ext => file.toLowerCase().endsWith(`.${ext}`)))
      .map(file => `/${directory}/${file}`);
  } catch (error) {
    console.error(`Error loading images from ${directory}:`, error);
    return [];
  }
};

/**
 * Get image files from a specific directory
 * This function returns known image files since we can't read directories in browser
 * @param {string} directory - Directory path
 * @returns {Promise<string[]>} - Array of image filenames
 */
const getImageFilesFromDirectory = async (directory) => {
  // Known image files in each directory
  const knownFiles = {
    'imgs/watches': [
      'watch_1.png',
      'watch_2.png',
      'watch_3.png',
      'watch_4.png',
      'watch_5.png',
      'watch_6.png'
    ],
    'imgs/bracelets': [
      'bracelet_1.png',
      'bracelet_2.png',
      'bracelet_3.png',
      'bracelet_4.png',
      'bracelet_5.png',
      'bracelet_6.png'
    ],
    'imgs/rings': [
      'ring.png'
    ],
    'imgs/earrings': [
      'earring.png'
    ],
    'imgs/watches_hero': [
      'watch_1_hero.png'
    ],
    'imgs/bracelets_hero': [
      'bracelet_1_hero.png'
    ],
    'imgs': [
      'ring.png',
      'earring.png'
    ],
    'imgs/bracelets_tryon': [
      'bracelet_1.png',
      'bracelet_2.png',
      'bracelet_3.png',
      'bracelet_4.png',
      'bracelet_5.png',
      'bracelet_6.png'
    ],
    'imgs/rings': [
      'ring_1.png',
      'ring_2.png',
      'ring_3.png',
      'ring_4.png',
      'ring_5.png',
      'ring_6.png'
    ],
    'imgs/earrings': [
      'earring_1.png',
      'earring_2.png',
      'earring_3.png',
      'earring_4.png',
      'earring_5.png',
      'earring_6.png'
    ]
  };

  return knownFiles[directory] || [];
};

/**
 * Load and process images with white background removal
 * @param {string} directory - Directory path
 * @param {boolean} removeBackground - Whether to remove white backgrounds
 * @param {number} tolerance - White detection tolerance
 * @returns {Promise<Array>} - Array of processed image objects
 */
export const loadAndProcessImages = async (directory, removeBackground = true, tolerance = 10) => {
  try {
    const imageUrls = await loadImagesFromDirectory(directory);
    const processedImages = [];

    for (let i = 0; i < imageUrls.length; i++) {
      const url = imageUrls[i];
      let processedUrl = url;

      if (removeBackground) {
        try {
          // Check if image has white background
          const hasWhiteBg = await hasWhiteBackground(url);

          if (hasWhiteBg) {
            // Remove white background
            processedUrl = await removeWhiteBackground(url, tolerance);
          }
        } catch (error) {
          console.warn(`Failed to process background for ${url}:`, error);
          // Use original image if processing fails
        }
      }

      processedImages.push({
        id: i + 1,
        original: url,
        processed: processedUrl,
        filename: url.split('/').pop()
      });
    }

    return processedImages;
  } catch (error) {
    console.error(`Error loading and processing images from ${directory}:`, error);
    return [];
  }
};

/**
 * Load watch images
 * @param {boolean} removeBackground - Whether to remove white backgrounds
 * @returns {Promise<Array>} - Array of watch image objects
 */
export const loadWatchImages = async (removeBackground = true) => {
  return loadAndProcessImages('imgs/watches', removeBackground);
};

/**
 * Load bracelet images
 * @param {boolean} removeBackground - Whether to remove white backgrounds
 * @returns {Promise<Array>} - Array of bracelet image objects
 */
export const loadBraceletImages = async (removeBackground = true) => {
  return loadAndProcessImages('imgs/bracelets', removeBackground);
};

/**
 * Load ring images
 * @param {boolean} removeBackground - Whether to remove white backgrounds
 * @returns {Promise<Array>} - Array of ring image objects
 */
export const loadRingImages = async (removeBackground = true) => {
  return loadAndProcessImages('imgs/rings', removeBackground);
};

/**
 * Load earring images
 * @param {boolean} removeBackground - Whether to remove white backgrounds
 * @returns {Promise<Array>} - Array of earring image objects
 */
export const loadEarringImages = async (removeBackground = true) => {
  return loadAndProcessImages('imgs/earrings', removeBackground);
};

/**
 * Load hero images (pre-processed without backgrounds)
 * @param {string} category - Category ('watches' or 'bracelets')
 * @returns {Promise<string>} - Hero image URL
 */
export const loadHeroImage = async (category) => {
  try {
    const heroDirectory = `imgs/${category}_hero`;
    const imageFiles = await getImageFilesFromDirectory(heroDirectory);

    if (imageFiles.length > 0) {
      // Return the first hero image (pre-processed)
      return `/${heroDirectory}/${imageFiles[0]}`;
    }

    // Fallback to regular images if hero images don't exist
    const fallbackImages = await getImageFilesFromDirectory(`imgs/${category}`);
    if (fallbackImages.length > 0) {
      return `/imgs/${category}/${fallbackImages[0]}`;
    }

    return null;
  } catch (error) {
    console.error(`Error loading hero image for ${category}:`, error);
    return null;
  }
};

/**
 * Load bracelet try-on images (already processed without backgrounds)
 * @returns {Promise<Array>} - Array of bracelet try-on image objects
 */
export const loadBraceletTryOnImages = async () => {
  try {
    const imageFiles = await getImageFilesFromDirectory('imgs/bracelets_tryon');
    return imageFiles.map((filename, index) => ({
      id: `bracelet_${index + 1}`,
      image: `/imgs/bracelets_tryon/${filename}`,
      filename,
      needsRotation: true // Flag to indicate these need vertical rotation
    }));
  } catch (error) {
    console.error('Error loading bracelet try-on images:', error);
    return [];
  }
};

/**
 * Load ring try-on images (already processed without backgrounds)
 * @returns {Promise<Array>} - Array of ring try-on image objects
 */
export const loadRingTryOnImages = async () => {
  try {
    const imageFiles = await getImageFilesFromDirectory('imgs/rings_tryon');
    return imageFiles.map((filename, index) => ({
      id: `ring_${index + 1}`,
      image: `/imgs/rings_tryon/${filename}`,
      filename,
      needsRotation: false // Rings don't need rotation
    }));
  } catch (error) {
    console.error('Error loading ring try-on images:', error);
    return [];
  }
};

/**
 * Load earring try-on images (already processed without backgrounds)
 * @returns {Promise<Array>} - Array of earring try-on image objects
 */
export const loadEarringTryOnImages = async () => {
  try {
    const imageFiles = await getImageFilesFromDirectory('imgs/earrings_tryon');
    return imageFiles.map((filename, index) => ({
      id: `earring_${index + 1}`,
      image: `/imgs/earrings_tryon/${filename}`,
      filename,
      needsRotation: false // Earrings don't need rotation
    }));
  } catch (error) {
    console.error('Error loading earring try-on images:', error);
    return [];
  }
};

/**
 * Generate product data from loaded images
 * @param {Array} images - Array of image objects from loadAndProcessImages
 * @param {string} category - Product category ('watches', 'bracelets', 'rings', or 'earrings')
 * @returns {Array} - Array of product objects
 */
export const generateProductsFromImages = (images, category) => {
  const baseNames = {
    watches: [
      'Classic Black',
      'Silver Chrono',
      'Gold Luxury',
      'Rose Gold',
      'Minimalist',
      'Sport Blue'
    ],
    bracelets: [
      'Silver Chain',
      'Gold Bangle',
      'Leather Wrap',
      'Diamond Tennis',
      'Beaded Stone',
      'Charm Bracelet'
    ],
    rings: [
      'Diamond Solitaire',
      'Gold Band',
      'Silver Ring',
      'Gemstone Ring',
      'Wedding Band'
    ],
    earrings: [
      'Diamond Studs',
      'Gold Hoops',
      'Pearl Drops',
      'Silver Earrings',
      'Gemstone Earrings'
    ]
  };

  const basePrices = {
    watches: ['$599', '$799', '$899', '$899', '$399', '$699'],
    bracelets: ['$299', '$349', '$249', '$599', '$199', '$399'],
    rings: ['$899', '$599', '$399', '$699', '$499'],
    earrings: ['$599', '$399', '$299', '$249', '$349']
  };

  const names = baseNames[category] || [];
  const prices = basePrices[category] || [];

  return images.map((image, index) => {
    // Generate string ID based on category and index
    const idMap = {
      watches: `watch_${index + 1}`,
      bracelets: `bracelet_${index + 1}`,
      rings: `ring_${index + 1}`,
      earrings: `earring_${index + 1}`
    };

    return {
      id: idMap[category] || `${category}_${index + 1}`,
      name: names[index] || `${category.slice(0, -1)} ${index + 1}`,
      image: image.processed, // Use processed image with background removed
      originalImage: image.original, // Keep reference to original
      price: prices[index] || '$299',
      filename: image.filename
    };
  });
};

/**
 * Load all product collections with processed images
 * @returns {Promise<Object>} - Object containing watches, bracelets, rings, and earrings collections
 */
export const loadProductCollections = async () => {
  try {
    const [watchImages, braceletImages, ringImages, earringImages] = await Promise.all([
      loadWatchImages(true),
      loadBraceletImages(true),
      loadRingImages(true),
      loadEarringImages(true)
    ]);

    const watches = generateProductsFromImages(watchImages, 'watches');
    const bracelets = generateProductsFromImages(braceletImages, 'bracelets');
    const rings = generateProductsFromImages(ringImages, 'rings');
    const earrings = generateProductsFromImages(earringImages, 'earrings');

    return {
      watches,
      bracelets,
      rings,
      earrings
    };
  } catch (error) {
    console.error('Error loading product collections:', error);
    return {
      watches: [],
      bracelets: [],
      rings: [],
      earrings: []
    };
  }
};

/**
 * Preload images for better performance
 * @param {string[]} imageUrls - Array of image URLs to preload
 * @returns {Promise<void>}
 */
export const preloadImages = async (imageUrls) => {
  const promises = imageUrls.map(url => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(url);
      img.onerror = () => reject(new Error(`Failed to load ${url}`));
      img.src = url;
    });
  });

  try {
    await Promise.all(promises);
    console.log('All images preloaded successfully');
  } catch (error) {
    console.warn('Some images failed to preload:', error);
  }
};

// Function to dynamically load all watches from the watches folder
export const loadAllWatches = async () => {
  try {
    // Get all watch files from the watches folder
    // To add more watches, simply add the filename to this array
    const watchFiles = [
      'watch_1.png',
      'watch_2.png', 
      'watch_3.png',
      'watch_4.png',
      'watch_5.png',
      'watch_6.png'
    ];

    // Create watch objects with metadata
    const watches = watchFiles.map((filename, index) => {
      const watchNumber = index + 1;
      
      // Generate watch names based on index
      const watchNames = [
        'Classic Black',
        'Silver Chrono', 
        'Gold Luxury',
        'Rose Gold',
        'Minimalist',
        'Sport Blue'
      ];
      
      // Generate watch types
      const watchTypes = [
        'dress',
        'sport', 
        'luxury',
        'fashion',
        'minimalist',
        'smartwatch'
      ];
      
      // Generate brands
      const watchBrands = [
        'Rolex',
        'Omega', 
        'Seiko',
        'Citizen',
        'Fossil',
        'Tissot'
      ];

      return {
        id: `watch_${watchNumber}`,
        name: watchNames[index] || `Watch ${watchNumber}`,
        image: `/imgs/watches/${filename}`,
        path: `/imgs/watches/${filename}`,
        // Default dimensions for watches (can be customized per watch)
        caseDiameter: 40 + (index % 3) * 2, // Vary between 40-44mm
        caseThickness: 12 + (index % 2) * 1, // Vary between 12-13mm
        totalWidth: 42 + (index % 3) * 1, // Vary between 42-44mm
        totalHeight: 47 + (index % 2) * 1, // Vary between 47-48mm
        dialDiameter: 31 + (index % 3) * 1, // Vary between 31-33mm
        type: watchTypes[index] || 'dress',
        dialSize: 40 + (index % 3) * 2, // Vary between 40-44mm
        categories: ['all', 'mens', 'womens', 'luxury', 'new'],
        price: 500 + (index * 100), // Vary prices
        brand: watchBrands[index] || 'Brand'
      };
    });

    return watches;
  } catch (error) {
    console.error('Error loading watches:', error);
    return [];
  }
};

// Function to automatically detect and load all watch files
export const loadWatchesAutoDetect = async () => {
  try {
    // Use the same approach as loadAllWatches
    return await loadAllWatches();
  } catch (error) {
    console.error('Error loading watches:', error);
    return [];
  }
};

// Function to dynamically load watches with custom metadata
export const loadWatchesWithMetadata = async () => {
  try {
    // This could be extended to load metadata from a JSON file
    // For now, we'll use the same approach as loadAllWatches
    return await loadAllWatches();
  } catch (error) {
    console.error('Error loading watches with metadata:', error);
    return [];
  }
};
